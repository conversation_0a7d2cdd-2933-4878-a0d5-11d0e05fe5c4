<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circles -->
  <circle cx="300" cy="200" r="180" fill="white" fill-opacity="0.1"/>
  <circle cx="300" cy="200" r="150" fill="white" fill-opacity="0.05"/>
  
  <!-- Abstract brain/network pattern -->
  <path d="M300,50 C400,50 450,150 450,200 C450,250 400,350 300,350 C200,350 150,250 150,200 C150,150 200,50 300,50" 
        stroke="white" stroke-width="2" stroke-opacity="0.3" fill="none"/>
  
  <!-- Connecting lines -->
  <line x1="300" y1="50" x2="300" y2="350" stroke="white" stroke-width="2" stroke-opacity="0.2"/>
  <line x1="150" y1="200" x2="450" y2="200" stroke="white" stroke-width="2" stroke-opacity="0.2"/>
  
  <!-- Dots/nodes -->
  <circle cx="300" cy="50" r="4" fill="white" fill-opacity="0.5"/>
  <circle cx="300" cy="350" r="4" fill="white" fill-opacity="0.5"/>
  <circle cx="150" cy="200" r="4" fill="white" fill-opacity="0.5"/>
  <circle cx="450" cy="200" r="4" fill="white" fill-opacity="0.5"/>
  
  <!-- Floating elements -->
  <circle cx="200" cy="100" r="8" fill="white" fill-opacity="0.3"/>
  <circle cx="400" cy="300" r="8" fill="white" fill-opacity="0.3"/>
  <circle cx="250" cy="250" r="6" fill="white" fill-opacity="0.3"/>
  <circle cx="350" cy="150" r="6" fill="white" fill-opacity="0.3"/>
</svg> 